import 'package:flutter_test/flutter_test.dart';
import 'package:darve/services/auth/models/auth_state.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:darve/models/User.dart';

void main() {
  group('AuthError Tests', () {
    test('should create invalid credentials error', () {
      final error = AuthError.invalidCredentials();
      expect(error.message, 'Wrong username or password');
      expect(error.code, 'INVALID_CREDENTIALS');
    });

    test('should create session expired error', () {
      final error = AuthError.sessionExpired();
      expect(error.message, 'Session Expired! Log in again');
      expect(error.code, 'SESSION_EXPIRED');
    });

    test('should create network error', () {
      final error = AuthError.networkError();
      expect(error.message, 'Can not establish connection with Darve backend');
      expect(error.code, 'NETWORK_ERROR');
    });

    test('should create social login error', () {
      final error = AuthError.socialLoginFailed('Google');
      expect(error.message, 'Google login failed. Please try again later.');
      expect(error.code, 'SOCIAL_LOGIN_FAILED');
    });

    test('should create error from exception', () {
      final error = AuthError.fromException(Exception('Authentication failed'));
      expect(error.message, 'Wrong username or password');
      expect(error.code, 'INVALID_CREDENTIALS');
    });

    test('should create unknown error for unrecognized exception', () {
      final error = AuthError.fromException(Exception('Some random error'));
      expect(error.message, 'Exception: Some random error');
      expect(error.code, 'UNKNOWN_ERROR');
    });

    test('should create password reset failed error', () {
      final error = AuthError.passwordResetFailed('Invalid email format');
      expect(error.message, 'Password reset failed: Invalid email format');
      expect(error.code, 'PASSWORD_RESET_FAILED');
    });

    test('should create invalid reset code error', () {
      final error = AuthError.invalidResetCode();
      expect(error.message, 'Invalid or expired reset code');
      expect(error.code, 'INVALID_RESET_CODE');
    });

    test('should create user not found for reset error', () {
      final error = AuthError.userNotFoundForReset();
      expect(error.message, 'No account found with that email or username');
      expect(error.code, 'USER_NOT_FOUND_FOR_RESET');
    });

    test('should create email verification failed error', () {
      final error = AuthError.emailVerificationFailed('Invalid email');
      expect(error.message, 'Email verification failed: Invalid email');
      expect(error.code, 'EMAIL_VERIFICATION_FAILED');
    });

    test('should create invalid verification code error', () {
      final error = AuthError.invalidVerificationCode();
      expect(error.message, 'Invalid or expired verification code');
      expect(error.code, 'INVALID_VERIFICATION_CODE');
    });

    test('should create email already verified error', () {
      final error = AuthError.emailAlreadyVerified();
      expect(error.message, 'Email is already verified');
      expect(error.code, 'EMAIL_ALREADY_VERIFIED');
    });

    test('should create invalid email format error', () {
      final error = AuthError.invalidEmailFormat();
      expect(error.message, 'Invalid email format');
      expect(error.code, 'INVALID_EMAIL_FORMAT');
    });
  });

  group('AuthState Tests', () {
    test('should create initial state', () {
      final state = AuthState.initial();
      expect(state.isLoggedIn, false);
      expect(state.token, null);
      expect(state.user, null);
      expect(state.loadingState, AuthLoadingState.idle);
      expect(state.error, null);
    });

    test('should create loading state', () {
      final state = AuthState.loading();
      expect(state.isLoggedIn, false);
      expect(state.loadingState, AuthLoadingState.loading);
    });

    test('should create authenticated state', () {
      final user = UserModel(
        id: '123',
        username: 'testuser',
        name: 'Test User',
        imageUri: 'https://example.com/avatar.jpg',
      );
      final state = AuthState.authenticated(token: 'test_token', user: user);
      
      expect(state.isLoggedIn, true);
      expect(state.token, 'test_token');
      expect(state.user, user);
      expect(state.loadingState, AuthLoadingState.success);
    });

    test('should create error state', () {
      final error = AuthError.invalidCredentials();
      final state = AuthState.error(error);
      
      expect(state.isLoggedIn, false);
      expect(state.loadingState, AuthLoadingState.error);
      expect(state.error, error);
    });

    test('should create unauthenticated state', () {
      final state = AuthState.unauthenticated();
      expect(state.isLoggedIn, false);
      expect(state.token, null);
      expect(state.user, null);
      expect(state.loadingState, AuthLoadingState.idle);
    });

    test('should copy with new values', () {
      final initialState = AuthState.initial();
      final newState = initialState.copyWith(
        isLoggedIn: true,
        loadingState: AuthLoadingState.success,
      );
      
      expect(newState.isLoggedIn, true);
      expect(newState.loadingState, AuthLoadingState.success);
      expect(newState.token, null); // Should remain unchanged
    });

    test('should clear error', () {
      final error = AuthError.invalidCredentials();
      final errorState = AuthState.error(error);
      final clearedState = errorState.clearError();
      
      expect(clearedState.error, null);
      expect(clearedState.loadingState, AuthLoadingState.idle);
    });

    test('should have proper equality', () {
      final state1 = AuthState.initial();
      final state2 = AuthState.initial();
      final state3 = AuthState.loading();
      
      expect(state1, equals(state2));
      expect(state1, isNot(equals(state3)));
    });
  });
}
