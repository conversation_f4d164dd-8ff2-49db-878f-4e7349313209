# Authentication Architecture Documentation

## Overview

This document describes the new authentication architecture implemented for the Darve mobile app. The new architecture uses GetX for state management and provides a clean, structured approach to handling authentication.

## Architecture Components

### 1. Error Models (`lib/services/auth/models/auth_error.dart`)

**AppError (Abstract Base Class)**
- Base error class for all application errors
- Contains message, code, and original error information

**AuthError (Authentication Specific)**
- Extends AppError for authentication-specific errors
- Factory constructors for common error scenarios:
  - `AuthError.invalidCredentials()`
  - `AuthError.sessionExpired()`
  - `AuthError.networkError()`
  - `AuthError.socialLoginFailed(provider)`
  - `AuthError.fromException(exception)` - Smart error mapping

**AuthLoadingState (Enum)**
- `idle` - No operation in progress
- `loading` - Authentication operation in progress
- `success` - Operation completed successfully
- `error` - Operation failed

### 2. State Model (`lib/services/auth/models/auth_state.dart`)

**AuthState Class**
- Immutable state container for authentication
- Properties:
  - `isLoggedIn: bool` - Authentication status
  - `token: String?` - JWT token
  - `user: UserModel?` - User information
  - `loadingState: AuthLoadingState` - Current operation state
  - `error: AuthError?` - Current error if any

**Factory Constructors**
- `AuthState.initial()` - Initial unauthenticated state
- `AuthState.loading()` - Loading state
- `AuthState.authenticated(token, user)` - Authenticated state
- `AuthState.error(error)` - Error state
- `AuthState.unauthenticated()` - Explicitly unauthenticated

### 3. Service Interface (`lib/services/auth/auth_service.dart`)

**AuthService (Abstract Class)**
- Extends GetxService for dependency injection
- Defines all authentication operations:
  - `signInWithEmailPassword(username, password)`
  - `signInWithGoogle()`
  - `signInWithFacebook()`
  - `signInWithApple()`
  - `register(username, password, email?, name?, imageUri?)`
  - `logout()`
  - `forgotPassword(emailOrUsername)` - Send password reset email
  - `resetPassword(emailOrUsername, code, newPassword)` - Reset password with code
  - `loadStoredAuth()`
  - `clearStoredAuth()`
  - `clearError()`

### 4. Service Implementation (`lib/services/auth/auth_service_impl.dart`)

**AuthServiceImpl Class**
- Concrete implementation of AuthService
- Uses existing AuthApi for backend communication
- Handles token storage in SharedPreferences
- Manages reactive state using GetX observables
- Provides error handling and state management

**Key Features:**
- Automatic error mapping from exceptions
- Token persistence and retrieval
- User data storage and management
- Reactive state updates

### 5. Dependency Injection (`lib/providers/auth_provider.dart`)

**AuthProvider Class**
- Singleton pattern for auth service access
- Handles service initialization
- Provides static accessors:
  - `AuthProvider.initialize()` - Initialize the service
  - `AuthProvider.instance` - Get service instance
  - `AuthProvider.auth` - Get service via GetX

## Usage Examples

### Initialization (in main.dart)
```dart
void main() async {
  // ... other initialization
  await AuthProvider.initialize();
  runApp(MyApp());
}
```

### Using in UI Components
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final authService = AuthProvider.auth;
    
    return Obx(() {
      final authState = authService.authState.value;
      
      if (authState.isLoggedIn) {
        return AuthenticatedView();
      } else {
        return LoginView();
      }
    });
  }
}
```

### Handling Authentication
```dart
// Sign in
await authService.signInWithEmailPassword(username, password);

// Check for errors
if (authService.error != null) {
  showError(authService.error!.message);
  authService.clearError();
}

// Social login
await authService.signInWithGoogle();

// Logout
await authService.logout();
```

## Migration Strategy

### Current State
- **Legacy MobX**: AuthStore and UserStore still exist (AuthStore used by other repositories)
- **New AuthService**: Complete implementation with all authentication features
- **Full Migration**: All authentication UI components now use new service

### Migration Completed
1. ✅ **Phase 1**: New auth service implemented with full feature parity
2. ✅ **Phase 2**: All UI components migrated (ScreenHandler, SignIn, ForgotPassword, Register)
3. ✅ **Phase 3**: AuthController completely removed
4. ✅ **Phase 4**: AuthRepository completely removed
5. 🟡 **Phase 5**: AuthStore still present (used by other repositories for JWT access)

### Benefits of New Architecture

1. **Consistency**: Single source of truth for authentication state
2. **Error Handling**: Structured error types with proper categorization
3. **Testability**: Clean interfaces and dependency injection
4. **Maintainability**: Separation of concerns and clear responsibilities
5. **Reactive UI**: Automatic UI updates based on state changes
6. **Type Safety**: Strong typing for all authentication operations

## Testing

The new architecture includes comprehensive unit tests:
- Error model validation
- State management testing
- Factory constructor verification
- State transitions and equality checks

Run tests with:
```bash
flutter test test/auth_service_test.dart
```

## Future Enhancements

1. **Biometric Authentication**: Add fingerprint/face ID support
2. **Token Refresh**: Automatic JWT token renewal
3. **Multi-Factor Authentication**: SMS/Email verification
4. **Session Management**: Advanced session handling
5. **Analytics**: Authentication event tracking

## Files Created/Modified

### New Files
- `lib/services/auth/models/auth_error.dart` - Error handling models
- `lib/services/auth/models/auth_state.dart` - Authentication state model
- `lib/services/auth/auth_service.dart` - Service interface
- `lib/services/auth/auth_service_impl.dart` - Service implementation
- `lib/providers/auth_provider.dart` - Dependency injection provider
- `test/auth_service_test.dart` - Unit tests
- `docs/auth_architecture.md` - Architecture documentation

### Modified Files
- `lib/main.dart` - Added auth provider initialization
- `lib/pages/ScreenHandler.dart` - Updated to use new auth service for state management
- `lib/pages/SignIn.dart` - Enhanced to use new auth service while preserving exact UI
- `lib/pages/forgot_password_page.dart` - Updated to use new auth service with proper error handling
- `lib/controllers/register_controller.dart` - Updated to use new auth service
- `lib/controllers/forgot_password_controller.dart` - Updated to use new auth service
- `lib/utils/styles.dart` - Added textDarkColor constant

### Removed Files
- `lib/utils/http-requests/auth/auth_repository.dart` - Deprecated auth repository (replaced by AuthService)
- `lib/controllers/auth_controller.dart` - Legacy GetX controller (replaced by AuthService)

### Legacy Files (Still Present)
- `lib/mobx/AuthStore/auth_store.dart` - Still used by other repositories for JWT token access

This architecture provides a solid foundation for authentication that can be extended and maintained easily while ensuring a consistent user experience across the application.
