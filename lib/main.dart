import 'package:darve/config.dart';
import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/controllers/chat_list_controller.dart';
import 'package:darve/pages/ScreenHandler.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/utils/http-requests/request_cache.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'pages/ChatPages/chat_screen.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  await dotenv.load();
  AppConfig.fromEnv();
  WidgetsFlutterBinding.ensureInitialized();

  //Stripe init
  Stripe.publishableKey = AppConfig.instance.stripePublishableKey;
  Stripe.merchantIdentifier = 'merchant.flutter.stripe.test';
  Stripe.urlScheme = 'darvemobile';
  await Stripe.instance.applySettings();

  // Initialize legacy MobX stores
  Darve.instance.init();

  // Initialize new auth service
  await AuthProvider.initialize();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});
  final ChatListController c = Get.put(ChatListController());
  final auth = Get.put(AuthController(), permanent: true);

  @override
  Widget build(BuildContext context) {
    RequestCache.getInstance();
    return GetMaterialApp(
      navigatorKey: navigatorKey,
      title: 'Darve',
      debugShowCheckedModeBanner: false,
      home: const ScreenHandler(),
      theme: ThemeData(
        fontFamily: 'Rubik',
        primaryColor: Styles.primaryColor, // Main color
        colorScheme: ColorScheme.fromSeed(
          seedColor: Styles.primaryColor, // Main color for Material 3
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor, // Button color
            foregroundColor: Colors.white, // Text color
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
            elevation: 0,
          ),
        ),
      ),
      routes: {
        '/chat-screen': (context) {
          final args =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, String?>;
          return ChatScreen(
            chatId: args['chatId']!,
            title: args['title']!,
            avatarUrl: args['avatarUrl']!,
            userId: args['userId']!,
          );
        },
      },
    );
  }
}
