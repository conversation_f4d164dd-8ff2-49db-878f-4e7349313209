import 'dart:convert';
import 'dart:io';

import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/pages/utility.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/http-requests/posts/postInDiscussion.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class PostsRepository {
  Future<List<Post>> getPosts(String username) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;

      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('JWT Token is missing.');
      }

      final response = await http.get(
        Uri.parse('${AppConfig.instance.apiUrl}/api/user/$username/posts'),
        headers: {
          'Accept': 'application/json',
          'Cookie': 'jwt=$jwtToken',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        List<Post> allPosts = [];
        for (var element in responseData['posts']) {
          allPosts.add(Post.fromJson(element));
        }
        return allPosts;
      } else {
        throw Exception('Failed to getPosts: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "getPosts");
      return [];
    }
  }

  Future<dynamic> createPost(
    String content,
    Future<bool> Function() cb, [
    String filePath = "",
  ]) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;
      final jwtToken = authStore.jwtToken;

      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('createPost JWT Token is missing.');
      }

      final postTitle = generateRandomTitle(32);

      final uri = Uri.parse('${AppConfig.instance.apiUrl}/api/user/post');
      final request = http.MultipartRequest('POST', uri)
        ..headers['Cookie'] = 'jwt=$jwtToken'
        ..headers['Accept'] = 'application/json'
        ..fields['title'] = postTitle
        ..fields['content'] = content
        ..fields['topic_id'] = "";

      if (filePath.isNotEmpty) {
        final file = File(filePath);
        final fileBytes = await file.readAsBytes();
        final fileName = file.uri.pathSegments.last;
        final multipartFile = http.MultipartFile.fromBytes(
          'file_1',
          fileBytes,
          filename: fileName,
        );
        request.files.add(multipartFile);
      }

      final response = await request.send();

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = await response.stream.bytesToString();
        debugPrint(responseData.toString());
        await cb();
        return responseData;
      } else {
        final errorData = await response.stream.bytesToString();
        throw Exception('Failed to post message: $errorData');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "createPost");
      return null;
    }
  }
}
