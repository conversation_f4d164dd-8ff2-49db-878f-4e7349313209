import 'dart:convert';

import 'package:darve/config.dart';
import 'package:darve/helpers/user_helper.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/models/user.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/jwt.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class AuthResponse {
  final String token;
  final UserModel user;

  AuthResponse({required this.token, required this.user});

  factory AuthResponse.fromMap(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      user: UserModel.fromMap(json['user']),
    );
  }
}

class AuthRepository {
  final AuthStore authStore = Darve.instance.authStore;
  final UserStore userStore = Darve.instance.userStore;

  Future<AuthResponse> signIn({
    required String username,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.instance.apiUrl}/api/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'password': password,
          'username': username,
        }),
      );

      String? jwtCookie = response.headers['set-cookie'];

      if (jwtCookie != null) {
        String? jwtToken = JwtUtils().extractJwt(jwtCookie);
        if (jwtToken != null) await authStore.setJWT(jwtToken);
      }

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final userData = responseData['user'];

        await userStore.setUserStore(
            userData['username'].toString(),
            UserIdHelper().getUserId(userData),
            userData['image_uri'].toString(),
            userData['full_name'].toString(),
            userData['bio'].toString(),
            userData['email'].toString());
        return AuthResponse.fromMap(responseData);
      } else {
        throw Exception('Login failed: ${response.body}');
      }
    } catch (error) {
      handleError(error.toString());
      rethrow;
    }
  }

  Future<AuthResponse> register(String username, String password, String? email,
      String? name, String? imageUri) async {
    try {
      final data = {
        'password': password,
        'username': username,
      };
      if (email != null) data['email'] = email;
      if (name != null) data['full_name'] = name;
      if (imageUri != null) data['image_uri'] = imageUri;

      final response = await http.post(
        Uri.parse('${AppConfig.instance.apiUrl}/api/register'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(data),
      );

      String? jwtCookie = response.headers['set-cookie'];

      if (jwtCookie != null) {
        String? jwtToken = JwtUtils().extractJwt(jwtCookie);
        if (jwtToken != null) await authStore.setJWT(jwtToken);
      }

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final userData = responseData['user'];

        await userStore.setUserStore(
            userData['username'].toString(),
            UserIdHelper().getUserId(userData),
            userData['image_uri'].toString(),
            userData['full_name'].toString(),
            userData['bio'].toString(),
            userData['email'].toString());
        return AuthResponse.fromMap(responseData);
      } else {
        throw Exception('Registration failed: ${response.body}');
      }
    } catch (error) {
      handleError(error.toString());
      rethrow;
    }
  }

  Future<void> forgtoPassword(String usernameOrEmail) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.instance.apiUrl}/api/forgot_password'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'email_or_username': usernameOrEmail}),
      );

      if (response.statusCode == 200) {
        debugPrint('Password reset email sent successfully.');
      } else {
        throw Exception(response.body);
      }
    } catch (error) {
      await handleError(error.toString());
      rethrow;
    }
  }

  Future<void> handleError(String error) async {
    debugPrint('Error request: $error');
    if (error.contains("Authentication failed")) {
      ErrorsHandle().displayErrorToast("Wrong username or password");
    } else if (error.contains("username not found")) {
      ErrorsHandle().displayErrorToast("Wrong username or password");
    } else if (error.contains("ClientException with SocketException")) {
      ErrorsHandle()
          .displayErrorToast("Can not establish connection with Darve backend");
    } else {
      ErrorsHandle().displayErrorToast(error);
    }
  }
}
