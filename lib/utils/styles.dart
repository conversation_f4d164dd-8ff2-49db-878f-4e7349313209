import 'package:flutter/material.dart';

class Styles {
  static const Color primaryColor = Color(0xff3213F1);
  static const Color foregroundColor = Color(0xFFF0F1F7);
  static const Color textLightColor = Colors.black54;
  static const Color textDarkColor = Colors.black87;
  static const Color chatBubbleLightBgColor = Color(0xfff4f5fb);
  static const Color testFieldBorderRegular = Colors.white; //Color(0xffe9e9e9);

  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    padding: const EdgeInsets.symmetric(
      horizontal: 25,
      vertical: 12,
    ),
  );

  static const TextStyle primaryButtonTextStyle =
      TextStyle(color: Colors.white, fontWeight: FontWeight.w600, fontSize: 14);

  static const TextStyle lightTextStyle = TextStyle(
      fontWeight: FontWeight.w400, fontSize: 16, color: Styles.textLightColor);

  static const TextStyle thinLightTextStyle = TextStyle(
      fontWeight: FontWeight.w400, fontSize: 14, color: Styles.textLightColor);

  static const TextStyle smallThinLightTextStyle = TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 12,
      letterSpacing: -0.4,
      color: Styles.textLightColor);

  static const TextStyle errorTextStyle =
      TextStyle(color: Colors.red, fontSize: 12.0, fontWeight: FontWeight.bold);
}
