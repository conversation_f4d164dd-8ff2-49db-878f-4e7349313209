import 'package:darve/controllers/forgot_password_controller.dart';
import 'package:darve/controllers/register_controller.dart';
import 'package:darve/pages/SignUp.dart';
import 'package:darve/pages/forgot_password_page.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignInPageNew extends StatefulWidget {
  const SignInPageNew({super.key});

  @override
  State<SignInPageNew> createState() => _SignInPageNewState();
}

class _SignInPageNewState extends State<SignInPageNew> {
  late AuthService authService;
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  bool isPasswordVisible = false;
  bool isContainerVisible = false;

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;
    
    // Animate container visibility
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          isContainerVisible = true;
        });
      }
    });
  }

  @override
  void dispose() {
    usernameController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _signIn() async {
    if (usernameController.text.isEmpty || passwordController.text.isEmpty) {
      _showErrorSnackBar('Please enter both username and password');
      return;
    }

    await authService.signInWithEmailPassword(
      usernameController.text.trim(),
      passwordController.text,
    );
  }

  Future<void> _signInWithGoogle() async {
    await authService.signInWithGoogle();
  }

  Future<void> _signInWithFacebook() async {
    await authService.signInWithFacebook();
  }

  Future<void> _signInWithApple() async {
    await authService.signInWithApple();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: Styles.primaryColor,
            height: double.infinity,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: SizedBox(
                width: 220,
                child: Image.asset(
                  "assets/images/darve-no-bg.png",
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          ),
          Obx(() {
            // Listen to auth state changes
            final authState = authService.authState.value;
            
            // Show error if there's one
            if (authState.error != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showErrorSnackBar(authState.error!.message);
                authService.clearError();
              });
            }

            return AnimatedPositioned(
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeOut,
              bottom: isContainerVisible
                  ? 0
                  : -MediaQuery.of(context).size.height * 0.75,
              left: 0,
              right: 0,
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  color: Colors.white,
                ),
                height: MediaQuery.of(context).size.height * 0.75,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Column(
                    children: [
                      const SizedBox(height: 40.0),
                      const Text(
                        "Welcome Back!",
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.w700,
                          color: Styles.textDarkColor,
                        ),
                      ),
                      const SizedBox(height: 8.0),
                      const Text(
                        "Sign in to continue",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: Styles.textLightColor,
                        ),
                      ),
                      const SizedBox(height: 40.0),
                      
                      // Username field
                      TextField(
                        controller: usernameController,
                        decoration: InputDecoration(
                          labelText: "Username",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.person_outline),
                        ),
                      ),
                      const SizedBox(height: 20.0),
                      
                      // Password field
                      TextField(
                        controller: passwordController,
                        obscureText: !isPasswordVisible,
                        decoration: InputDecoration(
                          labelText: "Password",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.lock_outline),
                          suffixIcon: IconButton(
                            icon: Icon(
                              isPasswordVisible
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                isPasswordVisible = !isPasswordVisible;
                              });
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 18.0),
                      
                      // Forgot password
                      GestureDetector(
                        onTap: () {
                          Get.to(() {
                            Get.put(ForgotPasswordController());
                            return ForgotPasswordPage();
                          })!.then(
                            (value) => Get.delete<ForgotPasswordController>(),
                          );
                        },
                        child: const Center(
                          child: Text(
                            "Forgot Password?",
                            style: TextStyle(
                              color: Styles.textLightColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 34.0),
                      
                      // Sign in button
                      Center(
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width * 0.675,
                          child: ElevatedButton(
                            onPressed: authState.loadingState == AuthLoadingState.loading
                                ? null
                                : _signIn,
                            child: authState.loadingState == AuthLoadingState.loading
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Text("Log in"),
                          ),
                        ),
                      ),
                      const SizedBox(height: 30.0),
                      
                      // Divider
                      const Row(
                        children: [
                          Expanded(child: Divider()),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              "or continue with",
                              style: TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          Expanded(child: Divider()),
                        ],
                      ),
                      const SizedBox(height: 22.0),
                      
                      // Social login buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            onPressed: authState.loadingState == AuthLoadingState.loading
                                ? null
                                : _signInWithGoogle,
                            icon: buildIconContainer(Icons.email_outlined),
                          ),
                          const SizedBox(width: 5.0),
                          IconButton(
                            onPressed: authState.loadingState == AuthLoadingState.loading
                                ? null
                                : _signInWithFacebook,
                            icon: buildIconContainerFromAsset(
                              'assets/images/sign_in/facebook.png',
                            ),
                          ),
                          const SizedBox(width: 5.0),
                          IconButton(
                            onPressed: authState.loadingState == AuthLoadingState.loading
                                ? null
                                : _signInWithApple,
                            icon: buildIconContainer(Icons.apple),
                          ),
                        ],
                      ),
                      const SizedBox(height: 35.0),
                      
                      // Sign up link
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            "New User? ",
                            style: TextStyle(
                              color: Styles.textLightColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Get.to(() {
                                Get.put(RegisterController());
                                return SignUpPage();
                              })!.then(
                                (value) => Get.delete<RegisterController>(),
                              );
                            },
                            child: const Text(
                              "Sign up",
                              style: TextStyle(
                                color: Styles.primaryColor,
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget buildIconContainer(IconData icon) {
    return CircleAvatar(
      radius: 25,
      backgroundColor: Styles.primaryColor,
      child: Icon(icon, color: Colors.white, size: 32),
    );
  }

  Widget buildIconContainerFromAsset(String assetPath) {
    return CircleAvatar(
      radius: 25,
      backgroundColor: Styles.primaryColor,
      child: Image.asset(
        assetPath,
        width: 32,
        height: 32,
        color: Colors.white,
      ),
    );
  }
}
