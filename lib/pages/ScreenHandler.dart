import 'package:darve/components/RecordingsPage/RecordingIcon.dart';
import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/pages/RecordingsPages/RecordingsPage.dart';
import 'package:darve/pages/ReelsPage.dart';
import 'package:darve/pages/profile_page.dart';
import 'package:darve/pages/ChatPages/chat_list_page.dart';
import 'package:darve/pages/SignIn.dart';
import 'package:darve/pages/WalletScreen.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScreenHandler extends StatefulWidget {
  const ScreenHandler({super.key});

  @override
  State<ScreenHandler> createState() => _ScreenHandlerState();
}

class _ScreenHandlerState extends State<ScreenHandler> {
  final UserStore userStore = Darve.instance.userStore;
  AuthController auth = Get.find<AuthController>();
  late AuthService authService;

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;
    _checkLoginStatus();
  }

  // This function will load the JWT and update the login status
  Future<void> _checkLoginStatus() async {
    await auth.loadJWT(); // Load JWT and set login status
    await userStore.loadUserStore(); // Load username & user id
    setState(() {});
  }

  int selectedPageIndex = 0;

  @override
  Widget build(BuildContext context) {
    void resetSelectedPage() {
      setState(() {
        selectedPageIndex = 0;
      });
    }

    Widget getSelectedPage(int _pageIdx) {
      final user = authService.user;

      // Fallback to legacy user store if new service doesn't have user data
      if (user == null) {
        userStore.loadUserStore();
        if (userStore.userId == null) {
          authService.logout();
          return SignInPage();
        }
      }

      if (_pageIdx == 0) {
        return const ReelsPage();
      } else if (_pageIdx == 1) {
        return ChatListPage();
      } else if (_pageIdx == 2) {
        return const WalletScreen();
      } else if (_pageIdx == 3) {
        // Use new auth service user data if available, otherwise fallback to legacy
        final userId = user?.id ?? userStore.userId!;
        final username = user?.username ?? userStore.username!;
        final imageUri = user?.imageUri ?? userStore.image_uri!;
        final fullName = user?.name ?? userStore.full_name!;

        return ProfilePage(
          userId,
          username,
          imageUri,
          fullName,
          goBackToReels: resetSelectedPage,
        );
      } else {
        return const RecordingsPage();
      }
    }

    return Obx(
      () {
        // Use new auth service for authentication state
        final isLoggedIn = authService.isLoggedIn;
        final user = authService.user;

        if (isLoggedIn && user != null) {
          return Scaffold(
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            floatingActionButton: FloatingActionButton(
              onPressed: () {
                setState(() {
                  selectedPageIndex = 4;
                });
              },
              shape: const CircleBorder(),
              backgroundColor: Styles.primaryColor,
              tooltip: "Create Challenge",
              child: selectedPageIndex == 4
                  ? const RecordingIcon()
                  : const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 35,
                    ),
            ),
            bottomNavigationBar: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .4),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: BottomAppBar(
                shape: const CircularNotchedRectangle(),
                notchMargin: 8.0,
                color: Colors.white,
                elevation: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildNavItem(
                          Icon(
                            Icons.home_outlined,
                            color: (selectedPageIndex == 0)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Home',
                          0),
                      _buildNavItem(
                          Image.asset(
                            'assets/images/navigation/chat.png',
                            width: 22,
                            height: 22,
                            color: (selectedPageIndex == 1)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Inbox',
                          1),
                      const SizedBox(width: 40),
                      _buildNavItem(
                          Icon(
                            Icons.wallet,
                            color: (selectedPageIndex == 2)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Wallet',
                          2),
                      _buildNavItem(
                          Icon(
                            Icons.person_outline,
                            color: (selectedPageIndex == 3)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Profile',
                          3),
                    ],
                  ),
                ),
              ),
            ),
            body: getSelectedPage(selectedPageIndex),
          );
        } else {
          return SignInPage(); // Show SignInPage if not logged in
        }
      },
    );
  }

  Widget _buildNavItem(Widget icon, String label, int index) {
    final isSelected = selectedPageIndex == index;
    return InkWell(
      onTap: () => setState(() => selectedPageIndex = index),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon,
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Styles.primaryColor : Colors.grey,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
