import 'package:darve/controllers/forgot_password_controller.dart';
import 'package:darve/controllers/register_controller.dart';
import 'package:darve/pages/SignUp.dart';
import 'package:darve/pages/forgot_password_page.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  String enteredUsername = '';
  String enteredPassword = '';
  late final AuthService authService;
  bool isPasswordVisible = false;
  bool isContainerVisible = false;

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;

    // Animate container visibility
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          isContainerVisible = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: Styles.primaryColor,
            height: double.infinity,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: SizedBox(
                width: 220,
                child: Image.asset(
                  "assets/images/darve-no-bg.png",
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          ),
          Obx(() {
            // Listen to auth state changes for error handling
            final authState = authService.authState.value;

            // Show error if there's one
            if (authState.error != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _handleAuthError(context);
              });
            }

            return AnimatedPositioned(
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeOut,
              bottom: isContainerVisible
                  ? 0
                  : -MediaQuery.of(context).size.height * 0.75,
              left: 0,
              right: 0,
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(50.0),
                    topRight: Radius.circular(50.0),
                  ),
                  color: Styles.foregroundColor,
                ),
                height: MediaQuery.of(context).size.height * 0.75,
                child: Column(
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(top: 32.0, bottom: 10.0),
                      child: Text(
                        "Login",
                        style: TextStyle(
                          fontSize: 22.0,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Text(
                      "Please login to continue...",
                      style: TextStyle(
                        color: Styles.textLightColor,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 16.0,
                        left: 24.0,
                        right: 24.0,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(left: 14, bottom: 4.0),
                            child: Text(
                              "Email",
                              style: TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 14.0,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          TextField(
                            onChanged: (value) {
                              enteredUsername = value;
                            },
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16.0),
                                borderSide: const BorderSide(
                                  color:
                                      Styles.primaryColor, // Your desired color
                                  width: 1.0,
                                ),
                              ),
                              filled: true,
                              hintText: "Username",
                              hintStyle: const TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 15.0,
                              ),
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16.0),
                                borderSide: BorderSide.none,
                              ),
                              prefixIcon: const SizedBox(
                                width: 16,
                                height: 16,
                                child: Center(
                                  child: Icon(
                                    Icons.person_outline,
                                    color: Styles.primaryColor,
                                  ),
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 15.0,
                                horizontal: 20.0,
                              ),
                            ),
                            style: const TextStyle(fontWeight: FontWeight.w400),
                          ),
                          const SizedBox(height: 10.0),
                          const Padding(
                            padding: EdgeInsets.only(left: 14, bottom: 4.0),
                            child: Text(
                              "Password",
                              style: TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 14.0,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          TextField(
                            onChanged: (value) {
                              enteredPassword = value;
                            },
                            obscureText: !isPasswordVisible,
                            obscuringCharacter: '*',
                            decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.0),
                                  borderSide: const BorderSide(
                                    color: Styles
                                        .primaryColor, // Your desired color
                                    width: 1.0,
                                  ),
                                ),
                                filled: true,
                                hintText: "********",
                                hintStyle: const TextStyle(
                                  color: Styles.textLightColor,
                                  fontSize: 16.0,
                                ),
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.0),
                                  borderSide: BorderSide.none,
                                ),
                                prefixIcon: SizedBox(
                                  width: 18,
                                  height: 16,
                                  child: Center(
                                    child: Image.asset(
                                      'assets/images/sign_in/lock.png',
                                      color: Styles.primaryColor,
                                      fit: BoxFit.contain,
                                      width: 16,
                                      height: 18,
                                    ),
                                  ),
                                ),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    isPasswordVisible
                                        ? Icons.visibility_outlined
                                        : Icons.visibility_off_outlined,
                                    color: Styles.primaryColor,
                                    size: 16,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      isPasswordVisible = !isPasswordVisible;
                                    });
                                  },
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  vertical: 15.0,
                                  horizontal: 20.0,
                                ),
                              ),
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const SizedBox(height: 18.0),
                          GestureDetector(
                            onTap: () {
                              Get.to(() {
                                Get.put(ForgotPasswordController());
                                return ForgotPasswordPage();
                              })!.then(
                                (value) =>
                                    Get.delete<ForgotPasswordController>(),
                              );
                            },
                            child: const Center(
                              child: Text(
                                "Forgot Password?",
                                style: TextStyle(
                                  color: Styles.textLightColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 34.0),
                          Center(
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width * 0.675,
                              child: ElevatedButton(
                                onPressed: () async {
                                  // Use new auth service
                                  await authService.signInWithEmailPassword(
                                    enteredUsername,
                                    enteredPassword,
                                  );
                                },
                                child: const Text("Log in"),
                              ),
                            ),
                          ),
                          const SizedBox(height: 40.0),
                          const Row(
                            children: <Widget>[
                              Expanded(
                                child: Divider(
                                  color: Styles.primaryColor,
                                  thickness: 1.74,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 12.0),
                                child: Text(
                                  "OR",
                                  style: TextStyle(
                                    color: Styles.primaryColor,
                                    fontSize: 12.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Divider(
                                  color: Styles.primaryColor,
                                  thickness: 1.74,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 22.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                onPressed: () async {
                                  await authService.signInWithGoogle();
                                },
                                icon: buildIconContainer(Icons.email_outlined),
                              ),
                              const SizedBox(width: 5.0),
                              IconButton(
                                onPressed: () async {
                                  await authService.signInWithFacebook();
                                },
                                icon: buildIconContainerFromAsset(
                                  'assets/images/sign_in/facebook.png',
                                ),
                              ),
                              const SizedBox(width: 5.0),
                              IconButton(
                                onPressed: () async {
                                  await authService.signInWithApple();
                                },
                                icon: buildIconContainer(Icons.apple),
                              ),
                            ],
                          ),
                          const SizedBox(height: 35.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                "New User? ",
                                style: TextStyle(
                                  color: Styles.textLightColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  Get.to(() {
                                    Get.put(RegisterController());
                                    return SignUpPage();
                                  })!.then(
                                    (value) => Get.delete<RegisterController>(),
                                  );
                                },
                                child: const Text(
                                  "Sign up",
                                  style: TextStyle(
                                    color: Styles.primaryColor,
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget buildIconContainer(IconData icon) {
    return CircleAvatar(
      radius: 25,
      backgroundColor: Styles.primaryColor,
      child: Icon(icon, color: Colors.white, size: 32),
    );
  }

  Widget buildIconContainerFromAsset(String assetPath) {
    return Image.asset(assetPath, width: 50, height: 50);
  }

  void _handleAuthError(BuildContext context) {
    if (authService.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authService.error!.message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 3),
        ),
      );
      authService.clearError();
    }
  }
}
