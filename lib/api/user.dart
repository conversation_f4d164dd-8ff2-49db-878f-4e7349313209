import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/utils/constants.dart';
import 'package:http/http.dart' as http;

class UserApi {
  final String jwtToken;

  const UserApi({required this.jwtToken});

  Future<void> setPassword(String password) async {
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/login'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
      body: json.encode({
        'password': password,
      }),
    );

    if (response.statusCode != 200) throw Exception(response.body);
  }

  Future<void> resetPassword(String oldPassowrd, String newPassword) async {
    final data = {
      'old_password': oldPassowrd,
      'new_username': newPassword,
    };

    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/register'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw ArgumentError(response.body);
  }

  Future<void> emailVeriticationStart(String email) async {
    final data = {'email': email};
    final response = await http.post(
      Uri.parse(
          '${AppConfig.instance.apiUrl}/api/users/current/email/verification/start'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw Exception(response.body);
  }

  Future<void> emailVarificationConfirm(String email, String code) async {
    final data = {
      'email': email,
      'code': code,
    };
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/users/current/email/verification/confirm'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw Exception(response.body);
  }
}
