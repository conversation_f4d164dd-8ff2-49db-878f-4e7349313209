import 'dart:convert';
import 'package:darve/api/models/auth_response_model.dart';
import 'package:darve/config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AuthRepository {
  Future<AuthResponseModel> login(String username, String password) async {
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/login'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode({'password': password, 'username': username}),
    );

    if (response.statusCode != 200) throw Exception(response.body);
    final responseData = json.decode(response.body);
    return AuthResponseModel.fromJson(responseData);
  }

  Future<AuthResponseModel> register(
    String username,
    String password,
    String? email,
    String? name,
    String? imageUri,
  ) async {
    final data = {'password': password, 'username': username};
    if (email != null) data['email'] = email;
    if (name != null) data['full_name'] = name;
    if (imageUri != null) data['image_uri'] = imageUri;

    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/register'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw ArgumentError(response.body);
    final responseData = json.decode(response.body);
    return AuthResponseModel.fromJson(responseData);
  }

  Future<void> forgotPassword(String emailOrUsername) async {
    final data = {'email_or_username': emailOrUsername};
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/forgot_password'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw ArgumentError(response.body);
  }

  Future<void> resetPassword(
    String emailOrUsername,
    String code,
    String password,
  ) async {
    final data = {
      'email_or_username': emailOrUsername,
      'code': code,
      'passowrd': password,
    };
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/reset_password'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw Exception(response.body);
  }

  Future<AuthResponseModel?> signWithFacebook() async {
    final result = await FacebookAuth.instance.login();
    if (result.status == LoginStatus.cancelled) return null;
    final accessToken = await FacebookAuth.instance.accessToken;
    if (accessToken == null) {
      throw Exception("Access Token invalid");
    }
    final body = {"token": accessToken.token};
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/auth/sign_with_facebook'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(body),
    );

    if (response.statusCode != 200) throw Exception(response.body);
    final responseData = json.decode(response.body);
    return AuthResponseModel.fromJson(responseData);
  }

  Future<AuthResponseModel> signWithApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    final data = {'token': credential.identityToken};

    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/auth/sign_with_apple'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 200) throw Exception(response.body);
    final responseData = json.decode(response.body);
    return AuthResponseModel.fromJson(responseData);
  }

  Future<AuthResponseModel> signWithGoogle() async {
    final scopes = ["email"];
    GoogleSignIn googleSignIn = defaultTargetPlatform == TargetPlatform.iOS
        ? GoogleSignIn(
            scopes: scopes,
            clientId: AppConfig.instance.googleIosClientId,
          )
        : defaultTargetPlatform == TargetPlatform.android
        ? GoogleSignIn(
            scopes: scopes,
            serverClientId: AppConfig.instance.googleAndroidClientId,
          )
        : throw Exception("Does not support");
    final account = await googleSignIn.signIn();
    if (account == null) throw Exception('Google sign-in aborted');

    final auth = await account.authentication;
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/auth/sign_with_google'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode({'token': auth.idToken}),
    );

    if (response.statusCode != 200) throw Exception(response.body);
    final responseData = json.decode(response.body);
    return AuthResponseModel.fromJson(responseData);
  }
}
