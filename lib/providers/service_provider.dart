import 'package:get_it/get_it.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/auth_service_impl.dart';
import 'package:darve/services/http/dio_service.dart';
import 'package:darve/repositories/auth_repository.dart';
import 'package:darve/repositories/user_repository.dart';
import 'package:darve/repositories/profile_repository.dart';

class ServiceProvider {
  static final GetIt _getIt = GetIt.instance;
  static bool _isInitialized = false;

  static GetIt get instance => _getIt;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Register DioService (depends on SimpleAuthService)
    _getIt.registerLazySingleton<DioService>(
      () => DioService(),
    );

    // Register Repository Interfaces with their implementations
    _getIt.registerLazySingleton<AuthRepository>(
      () => AuthRepository(_getIt<DioService>()),
    );

    _getIt.registerLazySingleton<UserRepository>(
      () => UserRepository(_getIt<DioService>()),
    );

    _getIt.registerLazySingleton<ProfileRepository>(
      () => ProfileRepository(_getIt<DioService>()),
    );

    // Step 4: Register full AuthService (depends on repositories)
    _getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl(
          authRepository: _getIt<AuthRepository>(),
        ));

    _isInitialized = true;
  }

  // Convenience getters
  static AuthService get authService => _getIt<AuthService>();

  static DioService get dioService => _getIt<DioService>();

  static AuthRepository get authRepository => _getIt<AuthRepository>();

  static UserRepository get userRepository => _getIt<UserRepository>();

  static ProfileRepository get profileRepository => _getIt<ProfileRepository>();

  static void reset() {
    _getIt.reset();
    _isInitialized = false;
  }

  static bool get isInitialized => _isInitialized;
}
