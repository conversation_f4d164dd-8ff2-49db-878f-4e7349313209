import 'package:get_it/get_it.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/auth_service_impl.dart';
import 'package:darve/services/auth/simple_auth_service.dart';
import 'package:darve/services/http/dio_service.dart';
import 'package:darve/repositories/interfaces/auth_repository_interface.dart';
import 'package:darve/repositories/interfaces/user_repository_interface.dart';
import 'package:darve/repositories/interfaces/profile_repository_interface.dart';
import 'package:darve/repositories/interfaces/challenge_repository_interface.dart';
import 'package:darve/repositories/interfaces/wallet_repository_interface.dart';
import 'package:darve/repositories/interfaces/chat_repository_interface.dart';
import 'package:darve/repositories/interfaces/posts_repository_interface.dart';
import 'package:darve/repositories/interfaces/notifications_repository_interface.dart';
import 'package:darve/repositories/auth_repository.dart';
import 'package:darve/repositories/user_repository.dart';
import 'package:darve/repositories/profile_repository.dart';
import 'package:darve/repositories/challenge_repository.dart';
import 'package:darve/repositories/wallet_repository.dart';
import 'package:darve/repositories/chat_repository.dart';
import 'package:darve/repositories/posts_repository.dart';
import 'package:darve/repositories/notifications_repository.dart';

class ServiceProvider {
  static final GetIt _getIt = GetIt.instance;
  static bool _isInitialized = false;

  static GetIt get instance => _getIt;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Register DioService (depends on SimpleAuthService)
    _getIt.registerLazySingleton<DioService>(
      () => DioService(),
    );

    // Register Repository Interfaces with their implementations
    _getIt.registerLazySingleton<AuthRepository>(
      () => AuthRepository(_getIt<DioService>()),
    );

    _getIt.registerLazySingleton<UserRepository>(
      () => UserRepository(_getIt<DioService>()),
    );

    _getIt.registerLazySingleton<ProfileRepository>(
      () => ProfileRepository(_getIt<DioService>()),
    );

    // Step 4: Register full AuthService (depends on repositories)
    _getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl(
          authRepository: _getIt<AuthRepository>(),
        ));

    _isInitialized = true;
  }

  // Convenience getters
  static AuthService get authService => _getIt<AuthService>();

  static DioService get dioService => _getIt<DioService>();

  static AuthRepository get authRepository => _getIt<AuthRepository>();

  static UserRepository get userRepository => _getIt<UserRepository>();

  static ProfileRepository get profileRepository => _getIt<ProfileRepository>();

  static void reset() {
    _getIt.reset();
    _isInitialized = false;
  }

  static bool get isInitialized => _isInitialized;
}
