import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/auth_service_impl.dart';
import 'package:get/get.dart';

class AuthProvider {
  static AuthService? _instance;

  // Initialize the auth service
  static Future<void> initialize() async {
    if (_instance == null) {
      _instance = AuthServiceImpl();
      Get.put<AuthService>(_instance!, permanent: true);
      await _instance!.initialize();
    }
  }

  // Get the auth service instance
  static AuthService get instance {
    if (_instance == null) {
      throw Exception(
        'AuthProvider is not initialized. Call AuthProvider.initialize() first.',
      );
    }
    return _instance!;
  }

  // Get auth service using GetX dependency injection
  static AuthService get auth => Get.find<AuthService>();

  // Check if auth service is initialized
  static bool get isInitialized => _instance != null;
}
