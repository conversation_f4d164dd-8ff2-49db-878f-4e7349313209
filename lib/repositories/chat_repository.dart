import 'package:darve/repositories/interfaces/chat_repository_interface.dart';
import 'package:darve/services/http/dio_service.dart';
import 'package:darve/utils/generateRandomTitle.dart';
import 'package:dio/dio.dart';

class ChatRepository implements ChatRepositoryInterface {
  final DioService _dioService;

  ChatRepository(this._dioService);

  @override
  Future<dynamic> getChatsList() async {
    final response = await _dioService.get('/api/user_chat/list');
    return response.data;
  }

  @override
  Future<dynamic> createChatWithUserId(String otherUserId) async {
    final response = await _dioService.get('/api/user_chat/with/$otherUserId');
    return response.data;
  }

  @override
  Future<dynamic> postMessageInChat(String discussionId, String content) async {
    final postTitle = generateRandomTitle(32);
    
    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    final response = await _dioService.post(
      '/api/discussion/$discussionId/post',
      data: formData,
    );
    return response.data;
  }

  @override
  Future<dynamic> getChatMessages(String chatId, {int limit = 50, int offset = 0}) async {
    final response = await _dioService.get(
      '/api/chat/$chatId/messages',
      queryParameters: {
        'limit': limit,
        'offset': offset,
      },
    );
    return response.data;
  }

  @override
  Future<dynamic> markChatAsRead(String chatId) async {
    final response = await _dioService.post('/api/chat/$chatId/read');
    return response.data;
  }

  @override
  Future<dynamic> deleteChatMessage(String messageId) async {
    final response = await _dioService.delete('/api/chat/message/$messageId');
    return response.data;
  }

  @override
  Future<dynamic> editChatMessage(String messageId, String newContent) async {
    final response = await _dioService.put(
      '/api/chat/message/$messageId',
      data: {'content': newContent},
    );
    return response.data;
  }
}
