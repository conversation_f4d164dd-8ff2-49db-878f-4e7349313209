import 'package:darve/models/DarveNotification.dart';
import 'package:darve/models/UserFollowAdded.dart';
import 'package:darve/models/UserTaskRequestCreated.dart';
import 'package:darve/repositories/interfaces/notifications_repository_interface.dart';
import 'package:darve/services/http/dio_service.dart';
import 'package:darve/utils/IdExtractor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class NotificationsRepository implements NotificationsRepositoryInterface {
  final DioService _dioService;

  NotificationsRepository(this._dioService);

  @override
  Future<List<DarveNotification>> getNotifications() async {
    List<DarveNotification> notifications = [];
    
    final response = await _dioService.get('/api/notification/user/history');
    
    for (var notif in (response.data as List)) {
      var val = notif['event']['value'];
      String type = notif['event']['type'];

      debugPrint("type ==$type val===$val");

      if (type == DarveNotification.userFollowAdded) {
        val = UserFollowAdded(
          username: val["username"],
          followedUser: val["follows_username"],
        );
      } else if (type == DarveNotification.userTaskRequestCreated) {
        val = UserTaskRequestCreated(
          taskId: IdExtractor.getIdent(val, propName: "task_id"),
          fromUser: IdExtractor.getIdent(val, propName: "from_user"),
          toUser: IdExtractor.getIdent(val, propName: "to_user"),
        );
      }

      notifications.add(DarveNotification.fromDetails(
        IdExtractor.getIdent(notif),
        type,
        val,
      ));
    }
    
    return notifications;
  }

  @override
  Stream<dynamic> getNotificationsSse() async* {
    try {
      final dio = _dioService.dio;
      final response = await dio.get(
        '/api/notification/user/sse',
        options: Options(
          headers: {'Content-Type': 'text/event-stream'},
          responseType: ResponseType.stream,
        ),
      );

      await for (final chunk in response.data.stream) {
        final data = String.fromCharCodes(chunk);
        if (data.isNotEmpty) {
          yield data;
        }
      }
    } catch (e) {
      throw Exception('Failed to connect to notifications SSE: $e');
    }
  }

  @override
  Future<dynamic> markNotificationAsRead(String notificationId) async {
    final response = await _dioService.post('/api/notification/$notificationId/read');
    return response.data;
  }

  @override
  Future<dynamic> markAllNotificationsAsRead() async {
    final response = await _dioService.post('/api/notification/read-all');
    return response.data;
  }

  @override
  Future<dynamic> deleteNotification(String notificationId) async {
    final response = await _dioService.delete('/api/notification/$notificationId');
    return response.data;
  }

  @override
  Future<dynamic> getUnreadNotificationsCount() async {
    final response = await _dioService.get('/api/notification/unread-count');
    return response.data;
  }
}
