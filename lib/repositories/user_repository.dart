import 'package:darve/services/http/dio_service.dart';

class UserRepository {
  final DioService _dioService;

  UserRepository(this._dioService);

  Future<void> setPassword(String password) async {
    await _dioService.post(
      '/api/login',
      data: {'password': password},
    );
  }

  Future<void> resetPassword(String oldPassword, String newPassword) async {
    await _dioService.post(
      '/api/register',
      data: {
        'old_password': oldPassword,
        'new_username': newPassword, // Note: keeping original field name
      },
    );
  }

  Future<void> emailVerificationStart(String email) async {
    await _dioService.post(
      '/api/users/current/email/verification/start',
      data: {'email': email},
    );
  }

  Future<void> emailVerificationConfirm(String email, String code) async {
    await _dioService.post(
      '/api/users/current/email/verification/confirm',
      data: {
        'email': email,
        'code': code,
      },
    );
  }
}
