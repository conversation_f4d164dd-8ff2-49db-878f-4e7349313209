abstract class ChallengeRepositoryInterface {
  Future<dynamic> getTaskRequests(String postId);
  Future<dynamic> getAllReceivedTaskRequests();
  Future<dynamic> getReceivedTaskRequestsForPost(String postId);
  Future<dynamic> acceptChallenge(String taskId, bool accepted);
  Future<dynamic> deliverTaskRequest(String taskId, String filePath, String postId);
  Future<dynamic> createChallenge(Map<String, dynamic> challengeData);
  Future<dynamic> getMyChallenges();
  Future<dynamic> getGivenChallenges();
  Future<dynamic> addParticipant(String challengeId, String userId);
}
