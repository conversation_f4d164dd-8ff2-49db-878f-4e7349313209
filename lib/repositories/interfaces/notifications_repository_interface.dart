import 'package:darve/models/DarveNotification.dart';

abstract class NotificationsRepositoryInterface {
  Future<List<DarveNotification>> getNotifications();
  Stream<dynamic> getNotificationsSse();
  Future<dynamic> markNotificationAsRead(String notificationId);
  Future<dynamic> markAllNotificationsAsRead();
  Future<dynamic> deleteNotification(String notificationId);
  Future<dynamic> getUnreadNotificationsCount();
}
