abstract class ChatRepositoryInterface {
  Future<dynamic> getChatsList();
  Future<dynamic> createChatWithUserId(String otherUserId);
  Future<dynamic> postMessageInChat(String discussionId, String content);
  Future<dynamic> getChatMessages(String chatId, {int limit = 50, int offset = 0});
  Future<dynamic> markChatAsRead(String chatId);
  Future<dynamic> deleteChatMessage(String messageId);
  Future<dynamic> editChatMessage(String messageId, String newContent);
}
