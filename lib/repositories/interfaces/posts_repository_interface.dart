import 'package:darve/models/Post.dart';

abstract class PostsRepositoryInterface {
  Future<List<Post>> getPosts(String username);
  Future<List<Post>> getFollowingPosts();
  Future<dynamic> createPost(String content, {String filePath = ""});
  Future<bool> postInDiscussion(String discussionId, String postUri, String content);
  Future<dynamic> likePost(String postId);
  Future<dynamic> unlikePost(String postId);
  Future<dynamic> sharePost(String postId);
  Future<dynamic> deletePost(String postId);
  Future<dynamic> editPost(String postId, String newContent);
  Future<dynamic> getPostComments(String postId);
  Future<dynamic> addComment(String postId, String comment);
}
