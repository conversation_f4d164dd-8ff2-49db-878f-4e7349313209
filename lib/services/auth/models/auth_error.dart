// Base error class for the application
abstract class AppError implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppError({
    required this.message,
    this.code,
    this.originalError,
  });

  @override
  String toString() => message;
}

// Authentication specific errors
class AuthError extends AppError {
  const AuthError({
    required super.message,
    super.code,
    super.originalError,
  });

  // Factory constructors for common auth errors
  factory AuthError.invalidCredentials() => const AuthError(
        message: 'Wrong username or password',
        code: 'INVALID_CREDENTIALS',
      );

  factory AuthError.userNotFound() => const AuthError(
        message: 'Wrong username or password',
        code: 'USER_NOT_FOUND',
      );

  factory AuthError.sessionExpired() => const AuthError(
        message: 'Session Expired! Log in again',
        code: 'SESSION_EXPIRED',
      );

  factory AuthError.networkError() => const AuthError(
        message: 'Can not establish connection with <PERSON><PERSON> backend',
        code: 'NETWORK_ERROR',
      );

  factory AuthError.socialLoginFailed(String provider) => AuthError(
        message: '$provider login failed. Please try again later.',
        code: 'SOCIAL_LOGIN_FAILED',
      );

  factory AuthError.tokenMissing() => const AuthError(
        message: 'Authentication token is missing',
        code: 'TOKEN_MISSING',
      );

  factory AuthError.registrationFailed(String reason) => AuthError(
        message: 'Registration failed: $reason',
        code: 'REGISTRATION_FAILED',
      );

  factory AuthError.unknown(dynamic error) => AuthError(
        message: error.toString(),
        code: 'UNKNOWN_ERROR',
        originalError: error,
      );

  // Helper method to create AuthError from exception
  factory AuthError.fromException(dynamic exception) {
    final errorString = exception.toString();
    
    if (errorString.contains("Authentication failed")) {
      return AuthError.invalidCredentials();
    } else if (errorString.contains("username not found")) {
      return AuthError.userNotFound();
    } else if (errorString.contains("The provided JWT")) {
      return AuthError.sessionExpired();
    } else if (errorString.contains("ClientException with SocketException")) {
      return AuthError.networkError();
    } else {
      return AuthError.unknown(exception);
    }
  }
}

// Loading states for auth operations
enum AuthLoadingState {
  idle,
  loading,
  success,
  error,
}
