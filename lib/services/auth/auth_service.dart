import 'package:darve/models/User.dart';
import 'package:darve/services/auth/models/auth_state.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:get/get.dart';

// Abstract interface for authentication service
abstract class AuthService extends GetxService {
  // Reactive state
  Rx<AuthState> get authState;
  
  // Convenience getters
  bool get isLoggedIn;
  String? get token;
  UserModel? get user;
  AuthLoadingState get loadingState;
  AuthError? get error;

  // Authentication methods
  Future<void> signInWithEmailPassword(String username, String password);
  Future<void> signInWithGoogle();
  Future<void> signInWithFacebook();
  Future<void> signInWithApple();
  Future<void> register({
    required String username,
    required String password,
    String? email,
    String? name,
    String? imageUri,
  });
  Future<void> logout();

  // Password recovery methods
  Future<void> forgotPassword(String emailOrUsername);
  Future<void> resetPassword({
    required String emailOrUsername,
    required String code,
    required String newPassword,
  });

  // Token management
  Future<void> loadStoredAuth();
  Future<void> clearStoredAuth();

  // Error handling
  void clearError();

  // Initialization
  Future<void> initialize();
}
