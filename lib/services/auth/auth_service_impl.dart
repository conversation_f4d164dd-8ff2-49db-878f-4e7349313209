import 'package:darve/api/auth.dart';
import 'package:darve/models/User.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/models/auth_state.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class AuthServiceImpl extends AuthService {
  final AuthApi _authApi = AuthApi();
  final Rx<AuthState> _authState = AuthState.initial().obs;

  @override
  Rx<AuthState> get authState => _authState;

  @override
  bool get isLoggedIn => _authState.value.isLoggedIn;

  @override
  String? get token => _authState.value.token;

  @override
  UserModel? get user => _authState.value.user;

  @override
  AuthLoadingState get loadingState => _authState.value.loadingState;

  @override
  AuthError? get error => _authState.value.error;

  @override
  Future<void> initialize() async {
    await loadStoredAuth();
  }

  @override
  Future<void> signInWithEmailPassword(String username, String password) async {
    try {
      _setLoadingState();
      
      final authResponse = await _authApi.login(username, password);
      
      await _handleSuccessfulAuth(authResponse.token, authResponse.user);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  @override
  Future<void> signInWithGoogle() async {
    try {
      _setLoadingState();
      
      final authResponse = await _authApi.signWithGoogle();
      
      await _handleSuccessfulAuth(authResponse.token, authResponse.user);
    } catch (e) {
      _handleAuthError(e, 'Google');
    }
  }

  @override
  Future<void> signInWithFacebook() async {
    try {
      _setLoadingState();
      
      final authResponse = await _authApi.signWithFacebook();
      if (authResponse == null) {
        _authState.value = AuthState.error(
          const AuthError(message: 'Facebook login was cancelled', code: 'LOGIN_CANCELLED'),
        );
        return;
      }
      
      await _handleSuccessfulAuth(authResponse.token, authResponse.user);
    } catch (e) {
      _handleAuthError(e, 'Facebook');
    }
  }

  @override
  Future<void> signInWithApple() async {
    try {
      _setLoadingState();
      
      final authResponse = await _authApi.signWithApple();
      
      await _handleSuccessfulAuth(authResponse.token, authResponse.user);
    } catch (e) {
      _handleAuthError(e, 'Apple');
    }
  }

  @override
  Future<void> register({
    required String username,
    required String password,
    String? email,
    String? name,
    String? imageUri,
  }) async {
    try {
      _setLoadingState();
      
      final authResponse = await _authApi.register(
        username,
        password,
        email,
        name,
        imageUri ?? 'https://avatars.githubusercontent.com/u/185073648?s=200&v=4',
      );
      
      await _handleSuccessfulAuth(authResponse.token, authResponse.user);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  @override
  Future<void> logout() async {
    try {
      await clearStoredAuth();
      _authState.value = AuthState.unauthenticated();
      debugPrint("User logged out successfully");
    } catch (e) {
      debugPrint("Error during logout: $e");
      // Even if there's an error, we should still clear the local state
      _authState.value = AuthState.unauthenticated();
    }
  }

  @override
  Future<void> loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('jwt_token');
      final userJson = prefs.getString('user');
      
      if (token != null && token.isNotEmpty && userJson != null) {
        final user = UserModel.fromJson(userJson);
        _authState.value = AuthState.authenticated(token: token, user: user);
      } else {
        _authState.value = AuthState.unauthenticated();
      }
    } catch (e) {
      debugPrint("Error loading stored auth: $e");
      _authState.value = AuthState.unauthenticated();
    }
  }

  @override
  Future<void> clearStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('jwt_token');
    await prefs.remove('user');
    // Also clear legacy user data
    await prefs.remove('username');
    await prefs.remove('userId');
    await prefs.remove('image_uri');
    await prefs.remove('full_name');
    await prefs.remove('bio');
    await prefs.remove('email');
  }

  @override
  void clearError() {
    _authState.value = _authState.value.clearError();
  }

  // Private helper methods
  void _setLoadingState() {
    _authState.value = _authState.value.copyWith(
      loadingState: AuthLoadingState.loading,
      error: null,
    );
  }

  Future<void> _handleSuccessfulAuth(String token, UserModel user) async {
    await _storeAuthData(token, user);
    _authState.value = AuthState.authenticated(token: token, user: user);
  }

  void _handleAuthError(dynamic error, [String? provider]) {
    AuthError authError;
    
    if (provider != null) {
      authError = AuthError.socialLoginFailed(provider);
    } else {
      authError = AuthError.fromException(error);
    }
    
    _authState.value = AuthState.error(authError);
    debugPrint("Auth error: $authError");
  }

  Future<void> _storeAuthData(String token, UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('jwt_token', token);
    await prefs.setString('user', user.toJson());
  }
}
