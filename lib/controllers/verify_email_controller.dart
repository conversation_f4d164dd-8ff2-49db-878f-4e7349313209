import 'package:darve/controllers/auth_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VerifyEmailController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController confirmEmailController = TextEditingController();
  final RxBool isEmailValid = false.obs;
  final RxBool areEmailsMatching = false.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isEmailChangedSuccess = false.obs;
  final RxString verificationCode = ''.obs;
  final RxBool isVerificationCodeSent = false.obs;
  final RxBool isVerificationCodeValid = false.obs;
  late final AuthController authController;

  @override
  void onInit() {
    super.onInit();
    authController = Get.find<AuthController>();
  }

  @override
  void onClose() {
    emailController.dispose();
    confirmEmailController.dispose();
    super.onClose();
  }

  bool validateEmail(String email) {
    // Simple regex for email validation
    final RegExp emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  bool matchEmails() {
    return emailController.text.trim() == confirmEmailController.text.trim();
  }

  Future<void> verificationStart() async {
    if (!validateEmail(emailController.text.trim())) {
      errorMessage.value = 'Invalid email format';
      isLoading.value = false;
      return;
    }
    if (!matchEmails()) {
      errorMessage.value = 'Emails do not match';
      isLoading.value = false;
      return;
    }
    isLoading.value = true;
    try {
      await authController.startEmailVerification(emailController.text.trim());

      // Check for errors from auth controller
      if (authController.error != null) {
        errorMessage.value = authController.error!.message;
        authController.clearError();
      } else {
        isVerificationCodeSent.value = true;
        errorMessage.value = '';
      }
    } catch (e) {
      errorMessage.value = '$e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> verificationConfirm() async {
    if (verificationCode.value.isEmpty) {
      errorMessage.value = 'Verification code cannot be empty';
      return;
    }
    isLoading.value = true;
    try {
      await authController.confirmEmailVerification(
          emailController.text.trim(), verificationCode.value);

      // Check for errors from auth controller
      if (authController.error != null) {
        errorMessage.value = authController.error!.message;
        authController.clearError();
      } else {
        isVerificationCodeValid.value = true;
        isEmailChangedSuccess.value = true;
        errorMessage.value = '';
      }
    } catch (e) {
      errorMessage.value = '$e';
    } finally {
      isLoading.value = false;
    }
  }
}
