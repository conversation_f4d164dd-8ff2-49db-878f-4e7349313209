import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VerifyEmailController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController confirmEmailController = TextEditingController();
  final RxBool isEmailValid = false.obs;
  final RxBool areEmailsMatching = false.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isEmailChangedSuccess = false.obs;
  final RxString verificationCode = ''.obs;
  final RxBool isVerificationCodeSent = false.obs;
  final RxBool isVerificationCodeValid = false.obs;
  late final AuthService authService;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
    _setupAuthServiceListeners();
  }

  // Setup listeners to sync with auth service state
  void _setupAuthServiceListeners() {
    // Observe loading state from auth service
    ever(authService.authState, (authState) {
      isLoading.value = authState.loadingState == AuthLoadingState.loading;
    });
  }

  @override
  void onClose() {
    emailController.dispose();
    confirmEmailController.dispose();
    super.onClose();
  }

  bool validateEmail(String email) {
    // Simple regex for email validation
    final RegExp emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  bool matchEmails() {
    return emailController.text.trim() == confirmEmailController.text.trim();
  }

  Future<void> verificationStart() async {
    if (!validateEmail(emailController.text.trim())) {
      errorMessage.value = 'Invalid email format';
      return;
    }
    if (!matchEmails()) {
      errorMessage.value = 'Emails do not match';
      return;
    }

    // Clear previous errors
    errorMessage.value = '';

    try {
      await authService.startEmailVerification(emailController.text.trim());

      // Check for errors from auth service
      if (authService.error != null) {
        errorMessage.value = authService.error!.message;
        authService.clearError();
      } else {
        isVerificationCodeSent.value = true;
        errorMessage.value = '';
      }
    } catch (e) {
      errorMessage.value = 'Failed to send verification code: $e';
    }
  }

  Future<void> verificationConfirm() async {
    if (verificationCode.value.isEmpty) {
      errorMessage.value = 'Verification code cannot be empty';
      return;
    }

    // Clear previous errors
    errorMessage.value = '';

    try {
      await authService.confirmEmailVerification(
          emailController.text.trim(), verificationCode.value);

      // Check for errors from auth service
      if (authService.error != null) {
        errorMessage.value = authService.error!.message;
        authService.clearError();
      } else {
        isVerificationCodeValid.value = true;
        isEmailChangedSuccess.value = true;
        errorMessage.value = '';
      }
    } catch (e) {
      errorMessage.value = 'Failed to verify email: $e';
    }
  }
}
