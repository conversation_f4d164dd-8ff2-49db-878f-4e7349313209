import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:darve/models/User.dart';
import 'package:get/get.dart';


class AuthController extends GetxController {
  RxBool isPasswordVisible = false.obs;
  RxBool isContainerVisible = false.obs;

  late final AuthService _authService;

  // Expose auth service state through controller properties
  RxString jwtToken = ''.obs;
  RxBool isLoggedIn = false.obs;
  UserModel? user;

  @override
  void onInit() {
    super.onInit();
    _authService = AuthProvider.auth;
    _setupAuthServiceListeners();
    _checkLoginStatus();
    
    Future.delayed(const Duration(milliseconds: 100), () {
      isContainerVisible.value = true;
    });
  }

  // Setup listeners to sync auth service state with controller state
  void _setupAuthServiceListeners() {
    ever(_authService.authState, (authState) {
      isLoggedIn.value = authState.isLoggedIn;
      jwtToken.value = authState.token ?? '';
      user = authState.user;
    });
  }

  Future<void> _checkLoginStatus() async {
    await loadJWT(); // Load JWT and set login status
    await loadUser(); // Load user data
  }

  // Authentication methods - delegate to auth service
  void signIn(String username, String password) async {
    await _authService.signInWithEmailPassword(username, password);
  }

  void signInWithApple() async {
    await _authService.signInWithApple();
  }

  void signInWithGoogle() async {
    await _authService.signInWithGoogle();
  }

  void signInWithFacebook() async {
    await _authService.signInWithFacebook();
  }

  void logout() async {
    await _authService.logout();
  }

  // JWT and user management methods - delegate to auth service
  Future<void> loadJWT() async {
    await _authService.loadStoredAuth();
  }

  Future<void> setJWT(String token) async {
    // This method is called by legacy code, but we'll let the auth service handle storage
    // The auth service will automatically update our state through the listener
  }

  Future<void> loadUser() async {
    // User data is automatically loaded by auth service
    // This method is kept for compatibility
  }

  Future<void> setUser(
    String username,
    String userId,
    String imageUri,
    String fullName,
    String bio,
    String email,
  ) async {
    // This method is called by legacy code
    // The auth service handles user data storage automatically
  }

  // Error handling - expose auth service errors
  AuthError? get error => _authService.error;
  
  void clearError() {
    _authService.clearError();
  }

  // Loading state - expose auth service loading state
  bool get isLoading => _authService.loadingState == AuthLoadingState.loading;


}
