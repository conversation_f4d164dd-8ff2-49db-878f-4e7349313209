import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:darve/models/User.dart';
import 'package:get/get.dart';


class AuthController extends GetxController {
  RxBool isPasswordVisible = false.obs;
  RxBool isContainerVisible = false.obs;

  late final AuthService _authService;

  // Expose auth service state through controller properties
  RxBool isLoggedIn = false.obs;
  UserModel? user;

  @override
  void onInit() {
    super.onInit();
    _initializeAuthService();

    Future.delayed(const Duration(milliseconds: 100), () {
      isContainerVisible.value = true;
    });
  }

  Future<void> _initializeAuthService() async {
    _authService = AuthProvider.auth;
    _setupAuthServiceListeners();
  }

  // Setup listeners to sync auth service state with controller state
  void _setupAuthServiceListeners() {
    ever(_authService.authState, (authState) {
      isLoggedIn.value = authState.isLoggedIn;
      user = authState.user;
    });
  }

  // Authentication methods - delegate to auth service
  void signIn(String username, String password) async {
    await _authService.signInWithEmailPassword(username, password);
  }

  void signInWithApple() async {
    await _authService.signInWithApple();
  }

  void signInWithGoogle() async {
    await _authService.signInWithGoogle();
  }

  void signInWithFacebook() async {
    await _authService.signInWithFacebook();
  }

  void logout() async {
    await _authService.logout();
  }

  Future<void> setUser(
    String username,
    String userId,
    String imageUri,
    String fullName,
    String bio,
    String email,
  ) async {
    // This method is called by legacy code
    // The auth service handles user data storage automatically
  }

  // Error handling - expose auth service errors
  AuthError? get error => _authService.error;
  
  void clearError() {
    _authService.clearError();
  }

  // Loading state - expose auth service loading state
  bool get isLoading => _authService.loadingState == AuthLoadingState.loading;


}
